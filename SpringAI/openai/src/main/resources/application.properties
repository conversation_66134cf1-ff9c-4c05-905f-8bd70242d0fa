spring.application.name=openai

# Server configuration
server.port=8080

# Logging pattern
logging.pattern.console=%green(%d{HH:mm:ss.SSS}) %blue(%-5level) %red([%thread]) %yellow(%logger{15}) - %msg%n

# OpenAI Configuration
#spring.ai.openai.api-key=${OPENAI_API_KEY}
spring.ai.model.chat=openai
spring.ai.openai.chat.options.model=ai/gemma3
spring.ai.openai.api-key=dummy
spring.ai.openai.chat.base-url=http://localhost:12434/engines
